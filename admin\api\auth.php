<?php
session_start();

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 管理员账号配置（实际项目中应该存储在数据库中并加密）
$admin_users = [
    'admin' => 'admin123',  // 用户名 => 密码
    'root' => 'root123'
];

$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            handleCheckLogin();
            break;
        case 'POST':
            handleLogin();
            break;
        default:
            throw new Exception('不支持的请求方法');
    }
} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'message' => $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}

function handleLogin() {
    global $admin_users;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['username']) || !isset($input['password'])) {
        throw new Exception('用户名和密码不能为空');
    }
    
    $username = trim($input['username']);
    $password = trim($input['password']);
    
    if (empty($username) || empty($password)) {
        throw new Exception('用户名和密码不能为空');
    }
    
    // 验证用户名和密码
    if (!isset($admin_users[$username]) || $admin_users[$username] !== $password) {
        // 记录登录失败日志
        logLoginAttempt($username, false);
        throw new Exception('用户名或密码错误');
    }
    
    // 登录成功，设置会话
    $_SESSION['admin_logged_in'] = true;
    $_SESSION['admin_username'] = $username;
    $_SESSION['login_time'] = time();
    
    // 记录登录成功日志
    logLoginAttempt($username, true);
    
    echo json_encode([
        'code' => 200,
        'message' => '登录成功',
        'data' => [
            'username' => $username,
            'login_time' => date('Y-m-d H:i:s')
        ]
    ], JSON_UNESCAPED_UNICODE);
}

function handleCheckLogin() {
    $action = isset($_GET['action']) ? $_GET['action'] : 'check';
    
    switch ($action) {
        case 'check':
            $logged_in = isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
            
            echo json_encode([
                'code' => 200,
                'message' => '检查登录状态成功',
                'data' => [
                    'logged_in' => $logged_in,
                    'username' => $logged_in ? $_SESSION['admin_username'] : null,
                    'login_time' => $logged_in ? date('Y-m-d H:i:s', $_SESSION['login_time']) : null
                ]
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'logout':
            // 注销登录
            session_destroy();
            
            echo json_encode([
                'code' => 200,
                'message' => '注销成功',
                'data' => null
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        default:
            throw new Exception('不支持的操作');
    }
}

function logLoginAttempt($username, $success) {
    $log_file = '../logs/admin_login.log';
    $log_dir = dirname($log_file);
    
    // 确保日志目录存在
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    $timestamp = date('Y-m-d H:i:s');
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
    $status = $success ? 'SUCCESS' : 'FAILED';
    
    $log_entry = "[{$timestamp}] {$status} - Username: {$username}, IP: {$ip}, User-Agent: {$user_agent}" . PHP_EOL;
    
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
}

// 检查登录状态的中间件函数
function requireLogin() {
    if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
        http_response_code(401);
        echo json_encode([
            'code' => 401,
            'message' => '请先登录',
            'data' => null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 检查会话是否过期（24小时）
    if (isset($_SESSION['login_time']) && (time() - $_SESSION['login_time']) > 86400) {
        session_destroy();
        http_response_code(401);
        echo json_encode([
            'code' => 401,
            'message' => '会话已过期，请重新登录',
            'data' => null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
}
?>
