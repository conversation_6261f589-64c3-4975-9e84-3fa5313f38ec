<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 引入数据库配置
require_once '../../db.php';

try {
    // 获取总用户数
    $totalUsersQuery = "SELECT COUNT(*) as total FROM users";
    $totalUsersResult = $mysqli->query($totalUsersQuery);
    $totalUsers = $totalUsersResult->fetch_assoc()['total'];

    // 获取今日注册用户数
    $today = date('Y-m-d');
    $todayRegistrationsQuery = "SELECT COUNT(*) as total FROM users WHERE DATE(time) = ?";
    $stmt = $mysqli->prepare($todayRegistrationsQuery);
    $stmt->bind_param('s', $today);
    $stmt->execute();
    $todayRegistrations = $stmt->get_result()->fetch_assoc()['total'];

    // 获取今日启动次数
    $todayLaunchesQuery = "SELECT `今日启动次数` as total FROM tongji LIMIT 1";
    $todayLaunchesResult = $mysqli->query($todayLaunchesQuery);
    $todayLaunches = 0;
    if ($todayLaunchesResult && $todayLaunchesResult->num_rows > 0) {
        $todayLaunches = $todayLaunchesResult->fetch_assoc()['total'];
    }

    // 获取VIP用户数
    $vipUsersQuery = "SELECT COUNT(*) as total FROM users WHERE vipcode = '1' AND viptime > NOW()";
    $vipUsersResult = $mysqli->query($vipUsersQuery);
    $vipUsers = $vipUsersResult->fetch_assoc()['total'];

    // 获取最近7天的注册数据
    $registrationTrendQuery = "
        SELECT 
            DATE(time) as date,
            COUNT(*) as count
        FROM users 
        WHERE time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        GROUP BY DATE(time)
        ORDER BY date ASC
    ";
    $registrationTrendResult = $mysqli->query($registrationTrendQuery);
    $registrationTrend = [];
    while ($row = $registrationTrendResult->fetch_assoc()) {
        $registrationTrend[] = $row;
    }

    // 获取用户状态分布
    $userStatusQuery = "
        SELECT 
            CASE 
                WHEN tokencode = 200 THEN 'normal'
                WHEN tokencode = 500 THEN 'banned'
                ELSE 'other'
            END as status,
            COUNT(*) as count
        FROM users
        GROUP BY tokencode
    ";
    $userStatusResult = $mysqli->query($userStatusQuery);
    $userStatus = [];
    while ($row = $userStatusResult->fetch_assoc()) {
        $userStatus[] = $row;
    }

    // 获取卡密统计
    $cardStatsQuery = "
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN kamicode = '0' THEN 1 ELSE 0 END) as unused,
            SUM(CASE WHEN kamicode = '1' THEN 1 ELSE 0 END) as used
        FROM kamis
    ";
    $cardStatsResult = $mysqli->query($cardStatsQuery);
    $cardStats = $cardStatsResult->fetch_assoc();

    // 获取最近的系统活动
    $recentActivityQuery = "
        SELECT 
            token,
            time,
            vipcode,
            tokencode
        FROM users 
        ORDER BY time DESC 
        LIMIT 10
    ";
    $recentActivityResult = $mysqli->query($recentActivityQuery);
    $recentActivity = [];
    while ($row = $recentActivityResult->fetch_assoc()) {
        $recentActivity[] = $row;
    }

    // 返回数据
    echo json_encode([
        'code' => 200,
        'message' => '数据获取成功',
        'data' => [
            'totalUsers' => (int)$totalUsers,
            'todayRegistrations' => (int)$todayRegistrations,
            'todayLaunches' => (int)$todayLaunches,
            'vipUsers' => (int)$vipUsers,
            'registrationTrend' => $registrationTrend,
            'userStatus' => $userStatus,
            'cardStats' => [
                'total' => (int)$cardStats['total'],
                'unused' => (int)$cardStats['unused'],
                'used' => (int)$cardStats['used']
            ],
            'recentActivity' => $recentActivity,
            'systemStatus' => [
                'server' => 'online',
                'database' => 'online',
                'api' => 'online'
            ]
        ]
    ], JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'message' => '数据获取失败: ' . $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}

$mysqli->close();
?>
