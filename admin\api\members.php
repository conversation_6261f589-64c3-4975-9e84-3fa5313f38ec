<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 引入数据库配置
require_once '../../db.php';

$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            if (isset($_GET['action'])) {
                switch ($_GET['action']) {
                    case 'list':
                        handleGetMembers();
                        break;
                    case 'stats':
                        handleGetMemberStats();
                        break;
                    default:
                        throw new Exception('不支持的操作');
                }
            } else {
                handleGetMembers();
            }
            break;
        case 'POST':
            handleRechargeVip();
            break;
        case 'PUT':
            handleUpdateVipStatus();
            break;
        default:
            throw new Exception('不支持的请求方法');
    }
} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'message' => $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}

function handleGetMembers() {
    global $mysqli;
    
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    $status = isset($_GET['status']) ? $_GET['status'] : '';
    
    $offset = ($page - 1) * $limit;
    
    // 构建查询条件
    $whereConditions = ["vipcode = '1'"];
    $params = [];
    $types = '';
    
    if (!empty($search)) {
        $whereConditions[] = "token LIKE ?";
        $params[] = "%$search%";
        $types .= 's';
    }
    
    if ($status !== '') {
        if ($status === 'active') {
            $whereConditions[] = "viptime > NOW()";
        } elseif ($status === 'expired') {
            $whereConditions[] = "viptime <= NOW()";
        }
    }
    
    $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);
    
    // 获取总数
    $countQuery = "SELECT COUNT(*) as total FROM users $whereClause";
    if (!empty($params)) {
        $countStmt = $mysqli->prepare($countQuery);
        if (!empty($types)) {
            $countStmt->bind_param($types, ...$params);
        }
        $countStmt->execute();
        $totalCount = $countStmt->get_result()->fetch_assoc()['total'];
    } else {
        $totalCount = $mysqli->query($countQuery)->fetch_assoc()['total'];
    }
    
    // 获取会员列表
    $query = "
        SELECT 
            token,
            time,
            vipcode,
            viptime,
            tokencode,
            CASE 
                WHEN viptime > NOW() THEN 'active'
                ELSE 'expired'
            END as vip_status,
            DATEDIFF(viptime, NOW()) as days_remaining
        FROM users 
        $whereClause
        ORDER BY viptime DESC 
        LIMIT ? OFFSET ?
    ";
    
    $stmt = $mysqli->prepare($query);
    $params[] = $limit;
    $params[] = $offset;
    $types .= 'ii';
    
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $members = [];
    while ($row = $result->fetch_assoc()) {
        $members[] = $row;
    }
    
    echo json_encode([
        'code' => 200,
        'message' => '获取会员列表成功',
        'data' => [
            'members' => $members,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $limit,
                'total' => (int)$totalCount,
                'total_pages' => ceil($totalCount / $limit)
            ]
        ]
    ], JSON_UNESCAPED_UNICODE);
}

function handleGetMemberStats() {
    global $mysqli;
    
    // 获取会员统计数据
    $statsQuery = "
        SELECT 
            COUNT(*) as total_vip,
            SUM(CASE WHEN viptime > NOW() THEN 1 ELSE 0 END) as active_vip,
            SUM(CASE WHEN viptime <= NOW() THEN 1 ELSE 0 END) as expired_vip,
            SUM(CASE WHEN DATE(time) = CURDATE() THEN 1 ELSE 0 END) as today_new_vip
        FROM users 
        WHERE vipcode = '1'
    ";
    
    $result = $mysqli->query($statsQuery);
    $stats = $result->fetch_assoc();
    
    // 获取即将到期的会员（7天内）
    $expiringQuery = "
        SELECT COUNT(*) as expiring_soon
        FROM users 
        WHERE vipcode = '1' 
        AND viptime > NOW() 
        AND viptime <= DATE_ADD(NOW(), INTERVAL 7 DAY)
    ";
    
    $expiringResult = $mysqli->query($expiringQuery);
    $expiringSoon = $expiringResult->fetch_assoc()['expiring_soon'];
    
    // 获取最近7天的会员充值趋势
    $trendQuery = "
        SELECT 
            DATE(time) as date,
            COUNT(*) as count
        FROM users 
        WHERE vipcode = '1' 
        AND time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        GROUP BY DATE(time)
        ORDER BY date ASC
    ";
    
    $trendResult = $mysqli->query($trendQuery);
    $trend = [];
    while ($row = $trendResult->fetch_assoc()) {
        $trend[] = $row;
    }
    
    echo json_encode([
        'code' => 200,
        'message' => '获取会员统计成功',
        'data' => [
            'total_vip' => (int)$stats['total_vip'],
            'active_vip' => (int)$stats['active_vip'],
            'expired_vip' => (int)$stats['expired_vip'],
            'today_new_vip' => (int)$stats['today_new_vip'],
            'expiring_soon' => (int)$expiringSoon,
            'trend' => $trend
        ]
    ], JSON_UNESCAPED_UNICODE);
}

function handleRechargeVip() {
    global $mysqli;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['token']) || empty($input['token'])) {
        throw new Exception('用户名不能为空');
    }
    
    if (!isset($input['days']) || $input['days'] <= 0) {
        throw new Exception('充值天数必须大于0');
    }
    
    $token = $input['token'];
    $days = (int)$input['days'];
    
    // 检查用户是否存在
    $checkQuery = "SELECT viptime, vipcode FROM users WHERE token = ?";
    $checkStmt = $mysqli->prepare($checkQuery);
    $checkStmt->bind_param('s', $token);
    $checkStmt->execute();
    $userResult = $checkStmt->get_result();
    
    if ($userResult->num_rows === 0) {
        throw new Exception('用户不存在');
    }
    
    $user = $userResult->fetch_assoc();
    
    // 计算新的到期时间
    $currentVipTime = $user['viptime'];
    $now = new DateTime();
    
    if ($user['vipcode'] == '1' && $currentVipTime && strtotime($currentVipTime) > time()) {
        // 如果用户已经是VIP且未过期，在现有时间基础上增加
        $newVipTime = new DateTime($currentVipTime);
        $newVipTime->add(new DateInterval("P{$days}D"));
    } else {
        // 如果用户不是VIP或已过期，从当前时间开始计算
        $newVipTime = clone $now;
        $newVipTime->add(new DateInterval("P{$days}D"));
    }
    
    // 更新用户VIP状态
    $updateQuery = "UPDATE users SET vipcode = '1', viptime = ? WHERE token = ?";
    $updateStmt = $mysqli->prepare($updateQuery);
    $newVipTimeStr = $newVipTime->format('Y-m-d H:i:s');
    $updateStmt->bind_param('ss', $newVipTimeStr, $token);
    
    if ($updateStmt->execute()) {
        echo json_encode([
            'code' => 200,
            'message' => '会员充值成功',
            'data' => [
                'token' => $token,
                'days' => $days,
                'new_vip_time' => $newVipTimeStr
            ]
        ], JSON_UNESCAPED_UNICODE);
    } else {
        throw new Exception('会员充值失败');
    }
}

function handleUpdateVipStatus() {
    global $mysqli;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['action'])) {
        throw new Exception('操作类型不能为空');
    }
    
    $action = $input['action'];
    
    switch ($action) {
        case 'update_expired':
            // 更新所有过期用户的VIP状态
            $updateQuery = "UPDATE users SET vipcode = '0' WHERE vipcode = '1' AND viptime <= NOW()";
            $result = $mysqli->query($updateQuery);
            
            echo json_encode([
                'code' => 200,
                'message' => '已更新过期用户状态',
                'data' => ['affected_rows' => $mysqli->affected_rows]
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        default:
            throw new Exception('不支持的操作');
    }
}

$mysqli->close();
?>
