// 管理后台主要JavaScript文件
class AdminPanel {
    constructor() {
        this.currentPage = 'dashboard';
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadPage('dashboard');
        this.updateTime();
        setInterval(() => this.updateTime(), 1000);
    }

    bindEvents() {
        // 侧边栏切换
        document.getElementById('sidebarToggle').addEventListener('click', () => {
            this.toggleSidebar();
        });

        // 导航链接点击
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const page = link.getAttribute('data-page');
                this.loadPage(page);
                this.setActiveNav(link);
            });
        });

        // 响应式处理
        window.addEventListener('resize', () => {
            this.handleResize();
        });
    }

    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('mainContent');
        
        if (window.innerWidth <= 768) {
            sidebar.classList.toggle('show');
        } else {
            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');
        }
    }

    handleResize() {
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('mainContent');
        
        if (window.innerWidth <= 768) {
            sidebar.classList.remove('collapsed');
            mainContent.classList.remove('expanded');
        } else {
            sidebar.classList.remove('show');
        }
    }

    setActiveNav(activeLink) {
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        activeLink.classList.add('active');
        
        // 更新面包屑
        const breadcrumb = document.querySelector('.page-breadcrumb');
        breadcrumb.textContent = activeLink.textContent.trim();
    }

    async loadPage(page) {
        this.currentPage = page;
        const contentArea = document.getElementById('pageContent');
        
        // 显示加载状态
        contentArea.innerHTML = `
            <div class="text-center py-5">
                <div class="loading"></div>
                <p class="mt-3 text-muted">正在加载页面...</p>
            </div>
        `;

        try {
            // 根据页面类型加载不同内容
            switch (page) {
                case 'dashboard':
                    await this.loadDashboard();
                    break;
                case 'users':
                    await this.loadUsers();
                    break;
                case 'members':
                    await this.loadMembers();
                    break;
                case 'cards':
                    await this.loadCards();
                    break;
                case 'statistics':
                    await this.loadStatistics();
                    break;
                case 'logs':
                    await this.loadLogs();
                    break;
                case 'settings':
                    await this.loadSettings();
                    break;
                default:
                    this.loadNotFound();
            }
        } catch (error) {
            console.error('页面加载失败:', error);
            this.loadError();
        }
    }

    async loadDashboard() {
        const content = `
            <div class="page-header">
                <h1 class="page-title">数据看板</h1>
                <p class="page-subtitle">系统运行状态和关键指标概览</p>
            </div>
            
            <!-- 统计卡片 -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-primary">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        总用户数</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalUsers">-</div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-people fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-success">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        今日注册</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="todayRegistrations">-</div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-person-plus fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-info">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        今日启动</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="todayLaunches">-</div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-play-circle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-warning">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        VIP用户</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="vipUsers">-</div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-star fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 图表区域 -->
            <div class="row">
                <div class="col-xl-8 col-lg-7">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">用户注册趋势</h6>
                        </div>
                        <div class="card-body">
                            <canvas id="registrationChart"></canvas>
                        </div>
                    </div>
                </div>

                <div class="col-xl-4 col-lg-5">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">系统状态</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>服务器状态</span>
                                    <span class="text-success"><i class="bi bi-circle-fill"></i> 正常</span>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>数据库连接</span>
                                    <span class="text-success"><i class="bi bi-circle-fill"></i> 正常</span>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>API服务</span>
                                    <span class="text-success"><i class="bi bi-circle-fill"></i> 正常</span>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>系统时间</span>
                                    <span id="currentTime">-</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.getElementById('pageContent').innerHTML = content;
        
        // 加载数据
        await this.loadDashboardData();
        this.initDashboardCharts();
    }

    async loadDashboardData() {
        try {
            // 加载统计数据
            const response = await fetch('api/dashboard.php');
            const data = await response.json();
            
            if (data.code === 200) {
                document.getElementById('totalUsers').textContent = data.data.totalUsers || 0;
                document.getElementById('todayRegistrations').textContent = data.data.todayRegistrations || 0;
                document.getElementById('todayLaunches').textContent = data.data.todayLaunches || 0;
                document.getElementById('vipUsers').textContent = data.data.vipUsers || 0;
            }
        } catch (error) {
            console.error('加载数据失败:', error);
        }
    }

    initDashboardCharts() {
        // 注册趋势图表
        const ctx = document.getElementById('registrationChart');
        if (ctx) {
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['7天前', '6天前', '5天前', '4天前', '3天前', '2天前', '昨天', '今天'],
                    datasets: [{
                        label: '注册用户数',
                        data: [12, 19, 3, 5, 2, 3, 10, 15],
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.1)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
    }

    updateTime() {
        const now = new Date();
        const timeString = now.toLocaleString('zh-CN');
        const timeElement = document.getElementById('currentTime');
        if (timeElement) {
            timeElement.textContent = timeString;
        }
    }

    async loadUsers() {
        const content = `
            <div class="page-header d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="page-title">用户管理</h1>
                    <p class="page-subtitle">管理系统用户，查看用户信息和状态</p>
                </div>
                <button class="btn btn-primary" onclick="adminPanel.showCreateUserModal()">
                    <i class="bi bi-plus-lg"></i> 添加用户
                </button>
            </div>

            <!-- 搜索和筛选 -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <input type="text" class="form-control" id="userSearch" placeholder="搜索用户名...">
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="statusFilter">
                                <option value="">所有状态</option>
                                <option value="normal">正常</option>
                                <option value="banned">封禁</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="vipFilter">
                                <option value="">所有类型</option>
                                <option value="vip">VIP用户</option>
                                <option value="expired">普通用户</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-outline-primary w-100" onclick="adminPanel.searchUsers()">
                                <i class="bi bi-search"></i> 搜索
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 用户列表 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">用户列表</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>用户名</th>
                                    <th>注册时间</th>
                                    <th>用户状态</th>
                                    <th>会员状态</th>
                                    <th>会员到期</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="usersTableBody">
                                <tr>
                                    <td colspan="6" class="text-center py-4">
                                        <div class="loading"></div>
                                        <p class="mt-2 text-muted">正在加载用户数据...</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer">
                    <nav id="usersPagination"></nav>
                </div>
            </div>

            <!-- 创建用户模态框 -->
            <div class="modal fade" id="createUserModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">添加用户</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="createUserForm">
                                <div class="mb-3">
                                    <label for="newUserToken" class="form-label">用户名</label>
                                    <input type="text" class="form-control" id="newUserToken" required>
                                </div>
                                <div class="mb-3">
                                    <label for="newUserStatus" class="form-label">用户状态</label>
                                    <select class="form-select" id="newUserStatus">
                                        <option value="200">正常</option>
                                        <option value="500">封禁</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="newUserVip" class="form-label">会员状态</label>
                                    <select class="form-select" id="newUserVip">
                                        <option value="0">普通用户</option>
                                        <option value="1">VIP用户</option>
                                    </select>
                                </div>
                                <div class="mb-3" id="vipTimeGroup" style="display: none;">
                                    <label for="newUserVipTime" class="form-label">会员到期时间</label>
                                    <input type="datetime-local" class="form-control" id="newUserVipTime">
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" onclick="adminPanel.createUser()">创建用户</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 编辑用户模态框 -->
            <div class="modal fade" id="editUserModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">编辑用户</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="editUserForm">
                                <input type="hidden" id="editUserToken">
                                <div class="mb-3">
                                    <label class="form-label">用户名</label>
                                    <input type="text" class="form-control" id="editUserTokenDisplay" readonly>
                                </div>
                                <div class="mb-3">
                                    <label for="editUserVip" class="form-label">会员状态</label>
                                    <select class="form-select" id="editUserVip">
                                        <option value="0">普通用户</option>
                                        <option value="1">VIP用户</option>
                                    </select>
                                </div>
                                <div class="mb-3" id="editVipTimeGroup">
                                    <label for="editUserVipTime" class="form-label">会员到期时间</label>
                                    <input type="datetime-local" class="form-control" id="editUserVipTime">
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" onclick="adminPanel.updateUser()">保存更改</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.getElementById('pageContent').innerHTML = content;

        // 绑定事件
        this.bindUserEvents();

        // 加载用户数据
        await this.loadUsersData();
    }

    async loadMembers() {
        const content = `
            <div class="page-header d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="page-title">会员管理</h1>
                    <p class="page-subtitle">管理VIP会员，充值和查看会员状态</p>
                </div>
                <div>
                    <button class="btn btn-success me-2" onclick="adminPanel.showRechargeModal()">
                        <i class="bi bi-plus-circle"></i> 充值会员
                    </button>
                    <button class="btn btn-warning" onclick="adminPanel.updateExpiredMembers()">
                        <i class="bi bi-arrow-clockwise"></i> 更新过期状态
                    </button>
                </div>
            </div>

            <!-- 会员统计卡片 -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card border-left-primary">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        总VIP用户</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalVipUsers">-</div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-star fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card border-left-success">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        活跃VIP</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="activeVipUsers">-</div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-check-circle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card border-left-warning">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        即将到期</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="expiringSoonUsers">-</div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-exclamation-triangle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card border-left-info">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        今日新增</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="todayNewVipUsers">-</div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-person-plus fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 搜索和筛选 -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <input type="text" class="form-control" id="memberSearch" placeholder="搜索用户名...">
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="memberStatusFilter">
                                <option value="">所有状态</option>
                                <option value="active">活跃VIP</option>
                                <option value="expired">已过期</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-outline-primary w-100" onclick="adminPanel.searchMembers()">
                                <i class="bi bi-search"></i> 搜索
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 会员列表 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">VIP会员列表</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>用户名</th>
                                    <th>注册时间</th>
                                    <th>VIP状态</th>
                                    <th>到期时间</th>
                                    <th>剩余天数</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="membersTableBody">
                                <tr>
                                    <td colspan="6" class="text-center py-4">
                                        <div class="loading"></div>
                                        <p class="mt-2 text-muted">正在加载会员数据...</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer">
                    <nav id="membersPagination"></nav>
                </div>
            </div>

            <!-- 充值会员模态框 -->
            <div class="modal fade" id="rechargeModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">充值会员</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="rechargeForm">
                                <div class="mb-3">
                                    <label for="rechargeUserToken" class="form-label">用户名</label>
                                    <input type="text" class="form-control" id="rechargeUserToken" required>
                                    <div class="form-text">请输入要充值的用户名</div>
                                </div>
                                <div class="mb-3">
                                    <label for="rechargeDays" class="form-label">充值天数</label>
                                    <input type="number" class="form-control" id="rechargeDays" min="1" required>
                                    <div class="form-text">输入要充值的天数</div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-success" onclick="adminPanel.rechargeVip()">确认充值</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.getElementById('pageContent').innerHTML = content;

        // 绑定事件
        this.bindMemberEvents();

        // 加载数据
        await this.loadMemberStats();
        await this.loadMembersData();
    }

    async loadCards() {
        const content = `
            <div class="page-header d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="page-title">卡密管理</h1>
                    <p class="page-subtitle">生成和管理系统卡密</p>
                </div>
                <div>
                    <button class="btn btn-success me-2" onclick="adminPanel.showGenerateCardModal()">
                        <i class="bi bi-plus-circle"></i> 生成卡密
                    </button>
                    <button class="btn btn-info me-2" onclick="adminPanel.exportCards()">
                        <i class="bi bi-download"></i> 导出卡密
                    </button>
                    <button class="btn btn-warning" onclick="adminPanel.deleteUsedCards()">
                        <i class="bi bi-trash"></i> 清理已用
                    </button>
                </div>
            </div>

            <!-- 卡密统计卡片 -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card border-left-primary">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        总卡密数</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalCards">-</div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-credit-card fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card border-left-success">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        未使用</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="unusedCards">-</div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-check-circle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card border-left-warning">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        已使用</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="usedCards">-</div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-x-circle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card border-left-info">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        今日生成</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="todayGenerated">-</div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-plus-square fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 搜索和筛选 -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <input type="text" class="form-control" id="cardSearch" placeholder="搜索卡密...">
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="cardStatusFilter">
                                <option value="">所有状态</option>
                                <option value="unused">未使用</option>
                                <option value="used">已使用</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-outline-primary w-100" onclick="adminPanel.searchCards()">
                                <i class="bi bi-search"></i> 搜索
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 卡密列表 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">卡密列表</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>卡密</th>
                                    <th>天数</th>
                                    <th>状态</th>
                                    <th>生成时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="cardsTableBody">
                                <tr>
                                    <td colspan="5" class="text-center py-4">
                                        <div class="loading"></div>
                                        <p class="mt-2 text-muted">正在加载卡密数据...</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer">
                    <nav id="cardsPagination"></nav>
                </div>
            </div>

            <!-- 生成卡密模态框 -->
            <div class="modal fade" id="generateCardModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">生成卡密</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="generateCardForm">
                                <div class="mb-3">
                                    <label for="cardCount" class="form-label">生成数量</label>
                                    <input type="number" class="form-control" id="cardCount" min="1" max="1000" required>
                                    <div class="form-text">最多可生成1000张卡密</div>
                                </div>
                                <div class="mb-3">
                                    <label for="cardDays" class="form-label">卡密天数</label>
                                    <input type="number" class="form-control" id="cardDays" min="1" required>
                                    <div class="form-text">每张卡密的有效天数</div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-success" onclick="adminPanel.generateCards()">生成卡密</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 导出卡密模态框 -->
            <div class="modal fade" id="exportCardModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">导出卡密</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="exportCardForm">
                                <div class="mb-3">
                                    <label for="exportStatus" class="form-label">导出状态</label>
                                    <select class="form-select" id="exportStatus">
                                        <option value="">所有卡密</option>
                                        <option value="unused">仅未使用</option>
                                        <option value="used">仅已使用</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="exportDays" class="form-label">指定天数（可选）</label>
                                    <input type="number" class="form-control" id="exportDays" min="1">
                                    <div class="form-text">留空则导出所有天数的卡密</div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-info" onclick="adminPanel.doExportCards()">导出</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.getElementById('pageContent').innerHTML = content;

        // 绑定事件
        this.bindCardEvents();

        // 加载数据
        await this.loadCardStats();
        await this.loadCardsData();
    }

    async loadStatistics() {
        const content = `
            <div class="page-header">
                <h1 class="page-title">统计分析</h1>
                <p class="page-subtitle">查看系统使用统计和数据分析</p>
            </div>

            <!-- 统计概览卡片 -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card border-left-primary">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        今日注册</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="todayRegistrations">-</div>
                                    <div class="text-xs" id="registrationGrowth">-</div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-person-plus fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card border-left-success">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        今日启动</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="todayLaunches">-</div>
                                    <div class="text-xs" id="launchGrowth">-</div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-play-circle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card border-left-info">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        总用户数</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalUsers">-</div>
                                    <div class="text-xs text-muted">活跃VIP: <span id="activeVipUsers">-</span></div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-people fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card border-left-warning">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        卡密使用率</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="cardUsageRate">-</div>
                                    <div class="text-xs text-muted">总计: <span id="totalCards">-</span> 张</div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-credit-card fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 图表区域 -->
            <div class="row mb-4">
                <div class="col-xl-8 col-lg-7">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3 d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-primary">用户注册趋势</h6>
                            <select class="form-select form-select-sm" id="registrationPeriod" style="width: auto;">
                                <option value="7">最近7天</option>
                                <option value="30" selected>最近30天</option>
                                <option value="90">最近90天</option>
                            </select>
                        </div>
                        <div class="card-body">
                            <canvas id="registrationTrendChart"></canvas>
                        </div>
                    </div>
                </div>

                <div class="col-xl-4 col-lg-5">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">用户状态分布</h6>
                        </div>
                        <div class="card-body">
                            <canvas id="userStatusChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-xl-6 col-lg-6">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">启动量趋势</h6>
                        </div>
                        <div class="card-body">
                            <canvas id="launchTrendChart"></canvas>
                        </div>
                    </div>
                </div>

                <div class="col-xl-6 col-lg-6">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">VIP用户分布</h6>
                        </div>
                        <div class="card-body">
                            <canvas id="vipDistributionChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 详细分析 -->
            <div class="row">
                <div class="col-xl-6 col-lg-6">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">注册时间分布（按小时）</h6>
                        </div>
                        <div class="card-body">
                            <canvas id="hourDistributionChart"></canvas>
                        </div>
                    </div>
                </div>

                <div class="col-xl-6 col-lg-6">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">VIP剩余时间分布</h6>
                        </div>
                        <div class="card-body">
                            <canvas id="vipRemainingChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.getElementById('pageContent').innerHTML = content;

        // 绑定事件
        this.bindStatisticsEvents();

        // 加载数据
        await this.loadStatisticsData();
    }

    async loadLogs() {
        // 日志查看页面将在后续实现
        document.getElementById('pageContent').innerHTML = `
            <div class="page-header">
                <h1 class="page-title">操作日志</h1>
                <p class="page-subtitle">查看系统操作日志和用户行为记录</p>
            </div>
            <div class="card">
                <div class="card-body text-center py-5">
                    <p class="text-muted">操作日志功能正在开发中...</p>
                </div>
            </div>
        `;
    }

    async loadSettings() {
        // 系统设置页面将在后续实现
        document.getElementById('pageContent').innerHTML = `
            <div class="page-header">
                <h1 class="page-title">系统设置</h1>
                <p class="page-subtitle">配置系统参数和应用设置</p>
            </div>
            <div class="card">
                <div class="card-body text-center py-5">
                    <p class="text-muted">系统设置功能正在开发中...</p>
                </div>
            </div>
        `;
    }

    loadNotFound() {
        document.getElementById('pageContent').innerHTML = `
            <div class="text-center py-5">
                <h2>页面未找到</h2>
                <p class="text-muted">请求的页面不存在</p>
            </div>
        `;
    }

    loadError() {
        document.getElementById('pageContent').innerHTML = `
            <div class="text-center py-5">
                <h2>加载失败</h2>
                <p class="text-muted">页面加载时发生错误，请稍后重试</p>
            </div>
        `;
    }

    // 用户管理相关方法
    bindUserEvents() {
        // VIP状态变化时显示/隐藏到期时间
        document.getElementById('newUserVip').addEventListener('change', (e) => {
            const vipTimeGroup = document.getElementById('vipTimeGroup');
            if (e.target.value === '1') {
                vipTimeGroup.style.display = 'block';
            } else {
                vipTimeGroup.style.display = 'none';
            }
        });

        document.getElementById('editUserVip').addEventListener('change', (e) => {
            const vipTimeGroup = document.getElementById('editVipTimeGroup');
            if (e.target.value === '1') {
                vipTimeGroup.style.display = 'block';
            } else {
                vipTimeGroup.style.display = 'none';
            }
        });

        // 搜索框回车事件
        document.getElementById('userSearch').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.searchUsers();
            }
        });
    }

    async loadUsersData(page = 1) {
        try {
            const search = document.getElementById('userSearch')?.value || '';
            const status = document.getElementById('statusFilter')?.value || '';
            const vipStatus = document.getElementById('vipFilter')?.value || '';

            const params = new URLSearchParams({
                page: page,
                limit: 20,
                search: search,
                status: status,
                vip_status: vipStatus
            });

            const response = await fetch(`api/users.php?${params}`);
            const data = await response.json();

            if (data.code === 200) {
                this.renderUsersTable(data.data.users);
                this.renderUsersPagination(data.data.pagination);
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            console.error('加载用户数据失败:', error);
            document.getElementById('usersTableBody').innerHTML = `
                <tr>
                    <td colspan="6" class="text-center py-4 text-danger">
                        加载失败: ${error.message}
                    </td>
                </tr>
            `;
        }
    }

    renderUsersTable(users) {
        const tbody = document.getElementById('usersTableBody');

        if (users.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center py-4 text-muted">
                        暂无用户数据
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = users.map(user => `
            <tr>
                <td>
                    <strong>${user.token}</strong>
                </td>
                <td>${new Date(user.time).toLocaleString('zh-CN')}</td>
                <td>
                    <span class="badge ${user.tokencode == 200 ? 'bg-success' : 'bg-danger'}">
                        ${user.status_text}
                    </span>
                </td>
                <td>
                    <span class="badge ${user.vip_status_text === 'VIP' ? 'bg-warning' : 'bg-secondary'}">
                        ${user.vip_status_text}
                    </span>
                </td>
                <td>
                    ${user.viptime && user.vipcode == '1' ?
                        new Date(user.viptime).toLocaleString('zh-CN') :
                        '-'
                    }
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="adminPanel.showEditUserModal('${user.token}', '${user.vipcode}', '${user.viptime}')">
                            <i class="bi bi-pencil"></i>
                        </button>
                        ${user.tokencode == 200 ?
                            `<button class="btn btn-outline-warning" onclick="adminPanel.banUser('${user.token}')">
                                <i class="bi bi-ban"></i>
                            </button>` :
                            `<button class="btn btn-outline-success" onclick="adminPanel.unbanUser('${user.token}')">
                                <i class="bi bi-check-circle"></i>
                            </button>`
                        }
                        <button class="btn btn-outline-danger" onclick="adminPanel.deleteUser('${user.token}')">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    renderUsersPagination(pagination) {
        const container = document.getElementById('usersPagination');

        if (pagination.total_pages <= 1) {
            container.innerHTML = '';
            return;
        }

        let paginationHtml = '<ul class="pagination justify-content-center mb-0">';

        // 上一页
        if (pagination.current_page > 1) {
            paginationHtml += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="adminPanel.loadUsersData(${pagination.current_page - 1})">上一页</a>
                </li>
            `;
        }

        // 页码
        const startPage = Math.max(1, pagination.current_page - 2);
        const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);

        for (let i = startPage; i <= endPage; i++) {
            paginationHtml += `
                <li class="page-item ${i === pagination.current_page ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="adminPanel.loadUsersData(${i})">${i}</a>
                </li>
            `;
        }

        // 下一页
        if (pagination.current_page < pagination.total_pages) {
            paginationHtml += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="adminPanel.loadUsersData(${pagination.current_page + 1})">下一页</a>
                </li>
            `;
        }

        paginationHtml += '</ul>';
        container.innerHTML = paginationHtml;
    }

    searchUsers() {
        this.loadUsersData(1);
    }

    showCreateUserModal() {
        const modal = new bootstrap.Modal(document.getElementById('createUserModal'));
        modal.show();
    }

    showEditUserModal(token, vipcode, viptime) {
        document.getElementById('editUserToken').value = token;
        document.getElementById('editUserTokenDisplay').value = token;
        document.getElementById('editUserVip').value = vipcode;

        if (viptime && viptime !== 'null') {
            const date = new Date(viptime);
            document.getElementById('editUserVipTime').value = date.toISOString().slice(0, 16);
        }

        const vipTimeGroup = document.getElementById('editVipTimeGroup');
        vipTimeGroup.style.display = vipcode === '1' ? 'block' : 'none';

        const modal = new bootstrap.Modal(document.getElementById('editUserModal'));
        modal.show();
    }

    async createUser() {
        try {
            const token = document.getElementById('newUserToken').value.trim();
            const tokencode = document.getElementById('newUserStatus').value;
            const vipcode = document.getElementById('newUserVip').value;
            const viptime = document.getElementById('newUserVipTime').value;

            if (!token) {
                alert('请输入用户名');
                return;
            }

            const userData = {
                token: token,
                tokencode: parseInt(tokencode),
                vipcode: parseInt(vipcode),
                viptime: viptime || null
            };

            const response = await fetch('api/users.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(userData)
            });

            const data = await response.json();

            if (data.code === 200) {
                alert('用户创建成功');
                bootstrap.Modal.getInstance(document.getElementById('createUserModal')).hide();
                document.getElementById('createUserForm').reset();
                this.loadUsersData();
            } else {
                alert('创建失败: ' + data.message);
            }
        } catch (error) {
            console.error('创建用户失败:', error);
            alert('创建失败: ' + error.message);
        }
    }

    async updateUser() {
        try {
            const token = document.getElementById('editUserToken').value;
            const vipcode = document.getElementById('editUserVip').value;
            const viptime = document.getElementById('editUserVipTime').value;

            const userData = {
                token: token,
                action: 'update_vip',
                vipcode: parseInt(vipcode),
                viptime: viptime || null
            };

            const response = await fetch('api/users.php', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(userData)
            });

            const data = await response.json();

            if (data.code === 200) {
                alert('用户信息更新成功');
                bootstrap.Modal.getInstance(document.getElementById('editUserModal')).hide();
                this.loadUsersData();
            } else {
                alert('更新失败: ' + data.message);
            }
        } catch (error) {
            console.error('更新用户失败:', error);
            alert('更新失败: ' + error.message);
        }
    }

    async banUser(token) {
        if (!confirm(`确定要封禁用户 "${token}" 吗？`)) {
            return;
        }

        try {
            const response = await fetch('api/users.php', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    token: token,
                    action: 'ban'
                })
            });

            const data = await response.json();

            if (data.code === 200) {
                alert('用户已封禁');
                this.loadUsersData();
            } else {
                alert('封禁失败: ' + data.message);
            }
        } catch (error) {
            console.error('封禁用户失败:', error);
            alert('封禁失败: ' + error.message);
        }
    }

    async unbanUser(token) {
        if (!confirm(`确定要解封用户 "${token}" 吗？`)) {
            return;
        }

        try {
            const response = await fetch('api/users.php', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    token: token,
                    action: 'unban'
                })
            });

            const data = await response.json();

            if (data.code === 200) {
                alert('用户已解封');
                this.loadUsersData();
            } else {
                alert('解封失败: ' + data.message);
            }
        } catch (error) {
            console.error('解封用户失败:', error);
            alert('解封失败: ' + error.message);
        }
    }

    async deleteUser(token) {
        if (!confirm(`确定要删除用户 "${token}" 吗？此操作不可恢复！`)) {
            return;
        }

        try {
            const response = await fetch('api/users.php', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    token: token
                })
            });

            const data = await response.json();

            if (data.code === 200) {
                alert('用户已删除');
                this.loadUsersData();
            } else {
                alert('删除失败: ' + data.message);
            }
        } catch (error) {
            console.error('删除用户失败:', error);
            alert('删除失败: ' + error.message);
        }
    }

    // 会员管理相关方法
    bindMemberEvents() {
        // 搜索框回车事件
        document.getElementById('memberSearch').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.searchMembers();
            }
        });
    }

    async loadMemberStats() {
        try {
            const response = await fetch('api/members.php?action=stats');
            const data = await response.json();

            if (data.code === 200) {
                document.getElementById('totalVipUsers').textContent = data.data.total_vip || 0;
                document.getElementById('activeVipUsers').textContent = data.data.active_vip || 0;
                document.getElementById('expiringSoonUsers').textContent = data.data.expiring_soon || 0;
                document.getElementById('todayNewVipUsers').textContent = data.data.today_new_vip || 0;
            }
        } catch (error) {
            console.error('加载会员统计失败:', error);
        }
    }

    async loadMembersData(page = 1) {
        try {
            const search = document.getElementById('memberSearch')?.value || '';
            const status = document.getElementById('memberStatusFilter')?.value || '';

            const params = new URLSearchParams({
                action: 'list',
                page: page,
                limit: 20,
                search: search,
                status: status
            });

            const response = await fetch(`api/members.php?${params}`);
            const data = await response.json();

            if (data.code === 200) {
                this.renderMembersTable(data.data.members);
                this.renderMembersPagination(data.data.pagination);
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            console.error('加载会员数据失败:', error);
            document.getElementById('membersTableBody').innerHTML = `
                <tr>
                    <td colspan="6" class="text-center py-4 text-danger">
                        加载失败: ${error.message}
                    </td>
                </tr>
            `;
        }
    }

    renderMembersTable(members) {
        const tbody = document.getElementById('membersTableBody');

        if (members.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center py-4 text-muted">
                        暂无会员数据
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = members.map(member => `
            <tr>
                <td>
                    <strong>${member.token}</strong>
                </td>
                <td>${new Date(member.time).toLocaleString('zh-CN')}</td>
                <td>
                    <span class="badge ${member.vip_status === 'active' ? 'bg-success' : 'bg-danger'}">
                        ${member.vip_status === 'active' ? '活跃' : '已过期'}
                    </span>
                </td>
                <td>
                    ${member.viptime ? new Date(member.viptime).toLocaleString('zh-CN') : '-'}
                </td>
                <td>
                    ${member.vip_status === 'active' && member.days_remaining >= 0 ?
                        `${member.days_remaining} 天` :
                        '已过期'
                    }
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-success" onclick="adminPanel.showRechargeModal('${member.token}')">
                            <i class="bi bi-plus-circle"></i> 充值
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    renderMembersPagination(pagination) {
        const container = document.getElementById('membersPagination');

        if (pagination.total_pages <= 1) {
            container.innerHTML = '';
            return;
        }

        let paginationHtml = '<ul class="pagination justify-content-center mb-0">';

        // 上一页
        if (pagination.current_page > 1) {
            paginationHtml += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="adminPanel.loadMembersData(${pagination.current_page - 1})">上一页</a>
                </li>
            `;
        }

        // 页码
        const startPage = Math.max(1, pagination.current_page - 2);
        const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);

        for (let i = startPage; i <= endPage; i++) {
            paginationHtml += `
                <li class="page-item ${i === pagination.current_page ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="adminPanel.loadMembersData(${i})">${i}</a>
                </li>
            `;
        }

        // 下一页
        if (pagination.current_page < pagination.total_pages) {
            paginationHtml += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="adminPanel.loadMembersData(${pagination.current_page + 1})">下一页</a>
                </li>
            `;
        }

        paginationHtml += '</ul>';
        container.innerHTML = paginationHtml;
    }

    searchMembers() {
        this.loadMembersData(1);
    }

    showRechargeModal(token = '') {
        document.getElementById('rechargeUserToken').value = token;
        document.getElementById('rechargeDays').value = '';

        const modal = new bootstrap.Modal(document.getElementById('rechargeModal'));
        modal.show();
    }

    async rechargeVip() {
        try {
            const token = document.getElementById('rechargeUserToken').value.trim();
            const days = parseInt(document.getElementById('rechargeDays').value);

            if (!token) {
                alert('请输入用户名');
                return;
            }

            if (!days || days <= 0) {
                alert('请输入有效的充值天数');
                return;
            }

            const response = await fetch('api/members.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    token: token,
                    days: days
                })
            });

            const data = await response.json();

            if (data.code === 200) {
                alert(`充值成功！用户 ${token} 已充值 ${days} 天会员`);
                bootstrap.Modal.getInstance(document.getElementById('rechargeModal')).hide();
                document.getElementById('rechargeForm').reset();
                this.loadMemberStats();
                this.loadMembersData();
            } else {
                alert('充值失败: ' + data.message);
            }
        } catch (error) {
            console.error('充值失败:', error);
            alert('充值失败: ' + error.message);
        }
    }

    async updateExpiredMembers() {
        if (!confirm('确定要更新所有过期用户的VIP状态吗？')) {
            return;
        }

        try {
            const response = await fetch('api/members.php', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: 'update_expired'
                })
            });

            const data = await response.json();

            if (data.code === 200) {
                alert(`已更新 ${data.data.affected_rows} 个过期用户的状态`);
                this.loadMemberStats();
                this.loadMembersData();
            } else {
                alert('更新失败: ' + data.message);
            }
        } catch (error) {
            console.error('更新失败:', error);
            alert('更新失败: ' + error.message);
        }
    }

    // 卡密管理相关方法
    bindCardEvents() {
        // 搜索框回车事件
        document.getElementById('cardSearch').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.searchCards();
            }
        });
    }

    async loadCardStats() {
        try {
            const response = await fetch('api/cards.php?action=stats');
            const data = await response.json();

            if (data.code === 200) {
                document.getElementById('totalCards').textContent = data.data.total_cards || 0;
                document.getElementById('unusedCards').textContent = data.data.unused_cards || 0;
                document.getElementById('usedCards').textContent = data.data.used_cards || 0;
                document.getElementById('todayGenerated').textContent = data.data.today_generated || 0;
            }
        } catch (error) {
            console.error('加载卡密统计失败:', error);
        }
    }

    async loadCardsData(page = 1) {
        try {
            const search = document.getElementById('cardSearch')?.value || '';
            const status = document.getElementById('cardStatusFilter')?.value || '';

            const params = new URLSearchParams({
                action: 'list',
                page: page,
                limit: 20,
                search: search,
                status: status
            });

            const response = await fetch(`api/cards.php?${params}`);
            const data = await response.json();

            if (data.code === 200) {
                this.renderCardsTable(data.data.cards);
                this.renderCardsPagination(data.data.pagination);
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            console.error('加载卡密数据失败:', error);
            document.getElementById('cardsTableBody').innerHTML = `
                <tr>
                    <td colspan="5" class="text-center py-4 text-danger">
                        加载失败: ${error.message}
                    </td>
                </tr>
            `;
        }
    }

    renderCardsTable(cards) {
        const tbody = document.getElementById('cardsTableBody');

        if (cards.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="5" class="text-center py-4 text-muted">
                        暂无卡密数据
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = cards.map(card => `
            <tr>
                <td>
                    <code class="text-primary">${card.kami}</code>
                    <button class="btn btn-sm btn-outline-secondary ms-2" onclick="adminPanel.copyToClipboard('${card.kami}')">
                        <i class="bi bi-clipboard"></i>
                    </button>
                </td>
                <td>
                    <span class="badge bg-info">${card.kamitimes} 天</span>
                </td>
                <td>
                    <span class="badge ${card.kamicode === '0' ? 'bg-success' : 'bg-secondary'}">
                        ${card.status_text}
                    </span>
                </td>
                <td>${new Date(card.time).toLocaleString('zh-CN')}</td>
                <td>
                    <button class="btn btn-sm btn-outline-danger" onclick="adminPanel.deleteCard('${card.kami}')">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    renderCardsPagination(pagination) {
        const container = document.getElementById('cardsPagination');

        if (pagination.total_pages <= 1) {
            container.innerHTML = '';
            return;
        }

        let paginationHtml = '<ul class="pagination justify-content-center mb-0">';

        // 上一页
        if (pagination.current_page > 1) {
            paginationHtml += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="adminPanel.loadCardsData(${pagination.current_page - 1})">上一页</a>
                </li>
            `;
        }

        // 页码
        const startPage = Math.max(1, pagination.current_page - 2);
        const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);

        for (let i = startPage; i <= endPage; i++) {
            paginationHtml += `
                <li class="page-item ${i === pagination.current_page ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="adminPanel.loadCardsData(${i})">${i}</a>
                </li>
            `;
        }

        // 下一页
        if (pagination.current_page < pagination.total_pages) {
            paginationHtml += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="adminPanel.loadCardsData(${pagination.current_page + 1})">下一页</a>
                </li>
            `;
        }

        paginationHtml += '</ul>';
        container.innerHTML = paginationHtml;
    }

    searchCards() {
        this.loadCardsData(1);
    }

    showGenerateCardModal() {
        document.getElementById('cardCount').value = '';
        document.getElementById('cardDays').value = '';

        const modal = new bootstrap.Modal(document.getElementById('generateCardModal'));
        modal.show();
    }

    async generateCards() {
        try {
            const count = parseInt(document.getElementById('cardCount').value);
            const days = parseInt(document.getElementById('cardDays').value);

            if (!count || count <= 0) {
                alert('请输入有效的生成数量');
                return;
            }

            if (!days || days <= 0) {
                alert('请输入有效的卡密天数');
                return;
            }

            if (count > 1000) {
                alert('单次生成数量不能超过1000张');
                return;
            }

            const response = await fetch('api/cards.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    count: count,
                    days: days
                })
            });

            const data = await response.json();

            if (data.code === 200) {
                alert(`成功生成 ${count} 张 ${days} 天的卡密`);
                bootstrap.Modal.getInstance(document.getElementById('generateCardModal')).hide();
                document.getElementById('generateCardForm').reset();
                this.loadCardStats();
                this.loadCardsData();
            } else {
                alert('生成失败: ' + data.message);
            }
        } catch (error) {
            console.error('生成卡密失败:', error);
            alert('生成失败: ' + error.message);
        }
    }

    exportCards() {
        const modal = new bootstrap.Modal(document.getElementById('exportCardModal'));
        modal.show();
    }

    async doExportCards() {
        try {
            const status = document.getElementById('exportStatus').value;
            const days = document.getElementById('exportDays').value;

            const params = new URLSearchParams({
                action: 'export',
                status: status,
                days: days || ''
            });

            const response = await fetch(`api/cards.php?${params}`);
            const data = await response.json();

            if (data.code === 200) {
                // 创建CSV内容
                let csvContent = "卡密,天数,状态,生成时间\n";
                data.data.cards.forEach(card => {
                    csvContent += `${card.kami},${card.kamitimes},${card.kamicode === '0' ? '未使用' : '已使用'},${card.time}\n`;
                });

                // 下载文件
                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', `卡密导出_${new Date().toISOString().slice(0, 10)}.csv`);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                bootstrap.Modal.getInstance(document.getElementById('exportCardModal')).hide();
                alert(`成功导出 ${data.data.total} 张卡密`);
            } else {
                alert('导出失败: ' + data.message);
            }
        } catch (error) {
            console.error('导出卡密失败:', error);
            alert('导出失败: ' + error.message);
        }
    }

    async deleteCard(kami) {
        if (!confirm(`确定要删除卡密 "${kami}" 吗？`)) {
            return;
        }

        try {
            const response = await fetch('api/cards.php', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: 'delete_single',
                    kami: kami
                })
            });

            const data = await response.json();

            if (data.code === 200) {
                alert('卡密删除成功');
                this.loadCardStats();
                this.loadCardsData();
            } else {
                alert('删除失败: ' + data.message);
            }
        } catch (error) {
            console.error('删除卡密失败:', error);
            alert('删除失败: ' + error.message);
        }
    }

    async deleteUsedCards() {
        if (!confirm('确定要删除所有已使用的卡密吗？此操作不可恢复！')) {
            return;
        }

        try {
            const response = await fetch('api/cards.php', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: 'delete_used'
                })
            });

            const data = await response.json();

            if (data.code === 200) {
                alert(`已删除 ${data.data.deleted_count} 张已使用的卡密`);
                this.loadCardStats();
                this.loadCardsData();
            } else {
                alert('删除失败: ' + data.message);
            }
        } catch (error) {
            console.error('删除失败:', error);
            alert('删除失败: ' + error.message);
        }
    }

    copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(() => {
            // 简单的提示
            const button = event.target.closest('button');
            const originalHtml = button.innerHTML;
            button.innerHTML = '<i class="bi bi-check"></i>';
            setTimeout(() => {
                button.innerHTML = originalHtml;
            }, 1000);
        }).catch(err => {
            console.error('复制失败:', err);
            alert('复制失败');
        });
    }

    // 统计分析相关方法
    bindStatisticsEvents() {
        // 注册趋势时间段选择
        document.getElementById('registrationPeriod').addEventListener('change', (e) => {
            this.loadRegistrationTrend(parseInt(e.target.value));
        });
    }

    async loadStatisticsData() {
        try {
            // 加载统计概览
            await this.loadStatisticsOverview();

            // 加载各种图表
            await this.loadRegistrationTrend(30);
            await this.loadLaunchTrend();
            await this.loadUserAnalysis();
            await this.loadVipAnalysis();
        } catch (error) {
            console.error('加载统计数据失败:', error);
        }
    }

    async loadStatisticsOverview() {
        try {
            const response = await fetch('api/statistics.php?action=overview');
            const data = await response.json();

            if (data.code === 200) {
                const stats = data.data;

                // 更新注册统计
                document.getElementById('todayRegistrations').textContent = stats.registration.today;
                const regGrowth = stats.registration.growth;
                const regGrowthElement = document.getElementById('registrationGrowth');
                regGrowthElement.textContent = `较昨日 ${regGrowth >= 0 ? '+' : ''}${regGrowth}%`;
                regGrowthElement.className = `text-xs ${regGrowth >= 0 ? 'text-success' : 'text-danger'}`;

                // 更新启动统计
                document.getElementById('todayLaunches').textContent = stats.launch.today;
                const launchGrowth = stats.launch.growth;
                const launchGrowthElement = document.getElementById('launchGrowth');
                launchGrowthElement.textContent = `较昨日 ${launchGrowth >= 0 ? '+' : ''}${launchGrowth}%`;
                launchGrowthElement.className = `text-xs ${launchGrowth >= 0 ? 'text-success' : 'text-danger'}`;

                // 更新用户统计
                document.getElementById('totalUsers').textContent = stats.users.total;
                document.getElementById('activeVipUsers').textContent = stats.users.active_vip;

                // 更新卡密统计
                document.getElementById('totalCards').textContent = stats.cards.total;
                const usageRate = stats.cards.total > 0 ?
                    ((stats.cards.used / stats.cards.total) * 100).toFixed(1) : 0;
                document.getElementById('cardUsageRate').textContent = `${usageRate}%`;
            }
        } catch (error) {
            console.error('加载统计概览失败:', error);
        }
    }

    async loadRegistrationTrend(days = 30) {
        try {
            const response = await fetch(`api/statistics.php?action=registration_trend&days=${days}`);
            const data = await response.json();

            if (data.code === 200) {
                this.renderRegistrationTrendChart(data.data);
            }
        } catch (error) {
            console.error('加载注册趋势失败:', error);
        }
    }

    async loadLaunchTrend() {
        try {
            const response = await fetch('api/statistics.php?action=launch_trend');
            const data = await response.json();

            if (data.code === 200) {
                this.renderLaunchTrendChart(data.data);
            }
        } catch (error) {
            console.error('加载启动趋势失败:', error);
        }
    }

    async loadUserAnalysis() {
        try {
            const response = await fetch('api/statistics.php?action=user_analysis');
            const data = await response.json();

            if (data.code === 200) {
                this.renderUserStatusChart(data.data.status_distribution);
                this.renderVipDistributionChart(data.data.vip_distribution);
                this.renderHourDistributionChart(data.data.hour_distribution);
            }
        } catch (error) {
            console.error('加载用户分析失败:', error);
        }
    }

    async loadVipAnalysis() {
        try {
            const response = await fetch('api/statistics.php?action=vip_analysis');
            const data = await response.json();

            if (data.code === 200) {
                this.renderVipRemainingChart(data.data.remaining_distribution);
            }
        } catch (error) {
            console.error('加载VIP分析失败:', error);
        }
    }

    renderRegistrationTrendChart(data) {
        const ctx = document.getElementById('registrationTrendChart');
        if (ctx) {
            // 销毁现有图表
            if (this.registrationChart) {
                this.registrationChart.destroy();
            }

            this.registrationChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.map(item => item.date),
                    datasets: [{
                        label: '注册用户数',
                        data: data.map(item => item.count),
                        borderColor: 'rgb(54, 162, 235)',
                        backgroundColor: 'rgba(54, 162, 235, 0.1)',
                        tension: 0.1,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
    }

    renderLaunchTrendChart(data) {
        const ctx = document.getElementById('launchTrendChart');
        if (ctx) {
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: data.map(item => item.date),
                    datasets: [{
                        label: '启动次数',
                        data: data.map(item => item.count),
                        backgroundColor: 'rgba(75, 192, 192, 0.6)',
                        borderColor: 'rgba(75, 192, 192, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
    }

    renderUserStatusChart(data) {
        const ctx = document.getElementById('userStatusChart');
        if (ctx) {
            const statusLabels = {
                'normal': '正常用户',
                'banned': '封禁用户',
                'other': '其他'
            };

            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: data.map(item => statusLabels[item.status] || item.status),
                    datasets: [{
                        data: data.map(item => item.count),
                        backgroundColor: [
                            'rgba(75, 192, 192, 0.8)',
                            'rgba(255, 99, 132, 0.8)',
                            'rgba(255, 205, 86, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }
    }

    renderVipDistributionChart(data) {
        const ctx = document.getElementById('vipDistributionChart');
        if (ctx) {
            const vipLabels = {
                'active_vip': '活跃VIP',
                'expired_vip': '过期VIP',
                'normal': '普通用户'
            };

            new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: data.map(item => vipLabels[item.vip_status] || item.vip_status),
                    datasets: [{
                        data: data.map(item => item.count),
                        backgroundColor: [
                            'rgba(255, 193, 7, 0.8)',
                            'rgba(220, 53, 69, 0.8)',
                            'rgba(108, 117, 125, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }
    }

    renderHourDistributionChart(data) {
        const ctx = document.getElementById('hourDistributionChart');
        if (ctx) {
            // 填充24小时数据
            const hourData = [];
            for (let i = 0; i < 24; i++) {
                const found = data.find(item => parseInt(item.hour) === i);
                hourData.push(found ? found.count : 0);
            }

            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: Array.from({length: 24}, (_, i) => `${i}:00`),
                    datasets: [{
                        label: '注册数量',
                        data: hourData,
                        backgroundColor: 'rgba(153, 102, 255, 0.6)',
                        borderColor: 'rgba(153, 102, 255, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
    }

    renderVipRemainingChart(data) {
        const ctx = document.getElementById('vipRemainingChart');
        if (ctx) {
            const remainingLabels = {
                'expired': '已过期',
                '1-7days': '1-7天',
                '8-30days': '8-30天',
                '31-90days': '31-90天',
                '90+days': '90天以上'
            };

            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: data.map(item => remainingLabels[item.remaining_period] || item.remaining_period),
                    datasets: [{
                        data: data.map(item => item.count),
                        backgroundColor: [
                            'rgba(220, 53, 69, 0.8)',
                            'rgba(255, 193, 7, 0.8)',
                            'rgba(255, 152, 0, 0.8)',
                            'rgba(40, 167, 69, 0.8)',
                            'rgba(23, 162, 184, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }
    }

    // 注销登录
    async logout() {
        if (!confirm('确定要注销登录吗？')) {
            return;
        }

        try {
            const response = await fetch('api/auth.php?action=logout');
            const data = await response.json();

            if (data.code === 200) {
                alert('注销成功');
                window.location.href = 'login.php';
            } else {
                alert('注销失败: ' + data.message);
            }
        } catch (error) {
            console.error('注销失败:', error);
            // 即使API调用失败，也跳转到登录页面
            window.location.href = 'login.php';
        }
    }
}

// 全局变量，方便在HTML中调用
let adminPanel;

// 初始化管理面板
document.addEventListener('DOMContentLoaded', () => {
    adminPanel = new AdminPanel();
});
