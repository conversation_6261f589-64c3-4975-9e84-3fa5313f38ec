<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 引入数据库配置
require_once '../../db.php';

$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            handleGetUsers();
            break;
        case 'POST':
            handleCreateUser();
            break;
        case 'PUT':
            handleUpdateUser();
            break;
        case 'DELETE':
            handleDeleteUser();
            break;
        default:
            throw new Exception('不支持的请求方法');
    }
} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'message' => $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}

function handleGetUsers() {
    global $mysqli;
    
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    $status = isset($_GET['status']) ? $_GET['status'] : '';
    $vipStatus = isset($_GET['vip_status']) ? $_GET['vip_status'] : '';
    
    $offset = ($page - 1) * $limit;
    
    // 构建查询条件
    $whereConditions = [];
    $params = [];
    $types = '';
    
    if (!empty($search)) {
        $whereConditions[] = "token LIKE ?";
        $params[] = "%$search%";
        $types .= 's';
    }
    
    if ($status !== '') {
        if ($status === 'normal') {
            $whereConditions[] = "tokencode = 200";
        } elseif ($status === 'banned') {
            $whereConditions[] = "tokencode = 500";
        }
    }
    
    if ($vipStatus !== '') {
        if ($vipStatus === 'vip') {
            $whereConditions[] = "vipcode = '1' AND viptime > NOW()";
        } elseif ($vipStatus === 'expired') {
            $whereConditions[] = "(vipcode = '0' OR viptime <= NOW())";
        }
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    // 获取总数
    $countQuery = "SELECT COUNT(*) as total FROM users $whereClause";
    if (!empty($params)) {
        $countStmt = $mysqli->prepare($countQuery);
        if (!empty($types)) {
            $countStmt->bind_param($types, ...$params);
        }
        $countStmt->execute();
        $totalCount = $countStmt->get_result()->fetch_assoc()['total'];
    } else {
        $totalCount = $mysqli->query($countQuery)->fetch_assoc()['total'];
    }
    
    // 获取用户列表
    $query = "
        SELECT 
            token,
            time,
            vipcode,
            viptime,
            tokencode,
            CASE 
                WHEN tokencode = 200 THEN '正常'
                WHEN tokencode = 500 THEN '封禁'
                ELSE '未知'
            END as status_text,
            CASE 
                WHEN vipcode = '1' AND viptime > NOW() THEN 'VIP'
                ELSE '普通'
            END as vip_status_text
        FROM users 
        $whereClause
        ORDER BY time DESC 
        LIMIT ? OFFSET ?
    ";
    
    $stmt = $mysqli->prepare($query);
    $params[] = $limit;
    $params[] = $offset;
    $types .= 'ii';
    
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $users = [];
    while ($row = $result->fetch_assoc()) {
        $users[] = $row;
    }
    
    echo json_encode([
        'code' => 200,
        'message' => '获取用户列表成功',
        'data' => [
            'users' => $users,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $limit,
                'total' => (int)$totalCount,
                'total_pages' => ceil($totalCount / $limit)
            ]
        ]
    ], JSON_UNESCAPED_UNICODE);
}

function handleCreateUser() {
    global $mysqli;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['token']) || empty($input['token'])) {
        throw new Exception('用户名不能为空');
    }
    
    $token = $input['token'];
    $vipcode = isset($input['vipcode']) ? $input['vipcode'] : 0;
    $viptime = isset($input['viptime']) ? $input['viptime'] : '';
    $tokencode = isset($input['tokencode']) ? $input['tokencode'] : 200;
    
    // 检查用户名是否已存在
    $checkQuery = "SELECT COUNT(*) as count FROM users WHERE token = ?";
    $checkStmt = $mysqli->prepare($checkQuery);
    $checkStmt->bind_param('s', $token);
    $checkStmt->execute();
    $exists = $checkStmt->get_result()->fetch_assoc()['count'] > 0;
    
    if ($exists) {
        throw new Exception('用户名已存在');
    }
    
    // 插入新用户
    $insertQuery = "INSERT INTO users (token, time, vipcode, viptime, tokencode) VALUES (?, NOW(), ?, ?, ?)";
    $insertStmt = $mysqli->prepare($insertQuery);
    $insertStmt->bind_param('sisi', $token, $vipcode, $viptime, $tokencode);
    
    if ($insertStmt->execute()) {
        echo json_encode([
            'code' => 200,
            'message' => '用户创建成功',
            'data' => ['token' => $token]
        ], JSON_UNESCAPED_UNICODE);
    } else {
        throw new Exception('用户创建失败');
    }
}

function handleUpdateUser() {
    global $mysqli;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['token']) || empty($input['token'])) {
        throw new Exception('用户名不能为空');
    }
    
    $token = $input['token'];
    $action = isset($input['action']) ? $input['action'] : '';
    
    switch ($action) {
        case 'ban':
            // 封禁用户
            $updateQuery = "UPDATE users SET tokencode = 500 WHERE token = ?";
            $stmt = $mysqli->prepare($updateQuery);
            $stmt->bind_param('s', $token);
            $stmt->execute();
            
            echo json_encode([
                'code' => 200,
                'message' => '用户已封禁',
                'data' => null
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'unban':
            // 解封用户
            $updateQuery = "UPDATE users SET tokencode = 200 WHERE token = ?";
            $stmt = $mysqli->prepare($updateQuery);
            $stmt->bind_param('s', $token);
            $stmt->execute();
            
            echo json_encode([
                'code' => 200,
                'message' => '用户已解封',
                'data' => null
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'update_vip':
            // 更新VIP状态
            $vipcode = isset($input['vipcode']) ? $input['vipcode'] : 0;
            $viptime = isset($input['viptime']) ? $input['viptime'] : '';
            
            $updateQuery = "UPDATE users SET vipcode = ?, viptime = ? WHERE token = ?";
            $stmt = $mysqli->prepare($updateQuery);
            $stmt->bind_param('iss', $vipcode, $viptime, $token);
            $stmt->execute();
            
            echo json_encode([
                'code' => 200,
                'message' => 'VIP状态更新成功',
                'data' => null
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        default:
            throw new Exception('不支持的操作');
    }
}

function handleDeleteUser() {
    global $mysqli;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['token']) || empty($input['token'])) {
        throw new Exception('用户名不能为空');
    }
    
    $token = $input['token'];
    
    // 删除用户
    $deleteQuery = "DELETE FROM users WHERE token = ?";
    $stmt = $mysqli->prepare($deleteQuery);
    $stmt->bind_param('s', $token);
    
    if ($stmt->execute()) {
        echo json_encode([
            'code' => 200,
            'message' => '用户删除成功',
            'data' => null
        ], JSON_UNESCAPED_UNICODE);
    } else {
        throw new Exception('用户删除失败');
    }
}

$mysqli->close();
?>
