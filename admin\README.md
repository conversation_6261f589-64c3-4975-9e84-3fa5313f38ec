# 系统管理后台

一个现代化的企业级系统管理后台，基于您现有的API系统构建，提供完整的用户管理、会员管理、卡密管理等功能。

## 功能特性

### 🎯 核心功能
- **数据看板** - 实时展示系统关键指标和运行状态
- **用户管理** - 查看、搜索、编辑、封禁/解封用户
- **会员管理** - VIP会员充值、状态管理、统计分析
- **卡密管理** - 生成、查看、导出、管理系统卡密
- **统计分析** - 详细的数据统计和趋势分析
- **操作日志** - 完整的用户操作记录查看
- **系统设置** - 软件信息、公告等系统配置

### 🎨 界面特性
- **响应式设计** - 完美适配桌面、平板、手机
- **现代化UI** - 基于Bootstrap 5的精美界面
- **交互友好** - 直观的操作流程和用户体验
- **数据可视化** - 丰富的图表展示

### 🔒 安全特性
- **登录认证** - 安全的管理员登录系统
- **会话管理** - 自动过期和安全注销
- **操作日志** - 完整的管理员操作记录

## 安装部署

### 环境要求
- PHP 7.4+
- MySQL 5.7+
- Web服务器（Apache/Nginx）

### 部署步骤

1. **上传文件**
   ```bash
   # 将admin目录上传到您的Web根目录
   cp -r admin/ /var/www/html/
   ```

2. **配置数据库**
   - 确保数据库连接配置正确（使用现有的db.php）
   - 数据库表结构应与现有API系统保持一致

3. **设置权限**
   ```bash
   # 设置适当的文件权限
   chmod -R 755 admin/
   chmod -R 777 admin/logs/  # 日志目录需要写权限
   ```

4. **配置管理员账号**
   编辑 `admin/api/auth.php` 文件，修改管理员账号：
   ```php
   $admin_users = [
       'admin' => 'your_password',  // 修改为您的密码
       'root' => 'your_password'    // 可以添加多个管理员
   ];
   ```

## 使用说明

### 登录系统
1. 访问 `http://your-domain/admin/login.php`
2. 使用配置的管理员账号登录
3. 默认账号：`admin` / `admin123`

### 主要功能

#### 数据看板
- 查看系统实时运行状态
- 关键指标统计（用户数、注册量、启动量等）
- 系统状态监控

#### 用户管理
- **查看用户列表** - 支持分页、搜索、筛选
- **用户操作** - 封禁/解封、编辑信息、删除用户
- **批量操作** - 支持批量管理用户
- **状态筛选** - 按用户状态、VIP状态筛选

#### 会员管理
- **VIP充值** - 给用户充值指定天数的会员
- **状态管理** - 查看会员到期时间、剩余天数
- **批量更新** - 一键更新所有过期用户状态
- **统计分析** - VIP用户数据统计

#### 卡密管理
- **生成卡密** - 批量生成指定天数的卡密
- **状态查看** - 查看已使用/未使用卡密
- **导出功能** - 支持CSV格式导出
- **批量清理** - 清理已使用的卡密

#### 统计分析
- **注册趋势** - 用户注册量趋势图表
- **启动统计** - 软件启动量统计
- **用户分析** - 用户状态分布、活跃度分析
- **时间分析** - 按小时统计注册分布

### API接口

后台提供了完整的RESTful API接口：

- `GET /admin/api/dashboard.php` - 获取数据看板数据
- `GET /admin/api/users.php` - 获取用户列表
- `POST /admin/api/users.php` - 创建用户
- `PUT /admin/api/users.php` - 更新用户
- `DELETE /admin/api/users.php` - 删除用户
- `GET /admin/api/members.php` - 获取会员数据
- `POST /admin/api/members.php` - 充值会员
- `GET /admin/api/cards.php` - 获取卡密列表
- `POST /admin/api/cards.php` - 生成卡密
- `GET /admin/api/statistics.php` - 获取统计数据

## 技术架构

### 前端技术
- **Bootstrap 5** - 响应式UI框架
- **Chart.js** - 数据可视化图表
- **Bootstrap Icons** - 图标库
- **原生JavaScript** - 无依赖的纯JS实现

### 后端技术
- **PHP** - 服务端语言
- **MySQL** - 数据库
- **RESTful API** - 标准化接口设计
- **Session** - 会话管理

### 安全措施
- **登录验证** - 基于Session的认证
- **SQL防注入** - 使用预处理语句
- **XSS防护** - 输出转义处理
- **CSRF防护** - 表单令牌验证

## 自定义扩展

### 添加新功能模块
1. 在侧边栏添加导航项
2. 创建对应的API接口
3. 实现前端页面和交互逻辑

### 修改样式
- 编辑 `admin/index.php` 中的CSS样式
- 或创建独立的CSS文件

### 扩展API
- 在 `admin/api/` 目录下创建新的PHP文件
- 遵循现有的API设计模式

## 故障排除

### 常见问题

1. **无法登录**
   - 检查管理员账号配置
   - 确认Session功能正常
   - 查看错误日志

2. **数据不显示**
   - 检查数据库连接
   - 确认API接口返回正常
   - 查看浏览器控制台错误

3. **权限错误**
   - 检查文件权限设置
   - 确认日志目录可写

### 日志文件
- 管理员登录日志：`admin/logs/admin_login.log`
- API操作日志：`api_logs/` 目录

## 更新日志

### v1.0.0 (2025-08-01)
- 初始版本发布
- 完整的管理后台功能
- 响应式设计
- 安全认证系统

## 技术支持

如有问题或建议，请联系开发团队。

---

**注意：** 请在生产环境中修改默认的管理员密码，并定期更新以确保系统安全。
