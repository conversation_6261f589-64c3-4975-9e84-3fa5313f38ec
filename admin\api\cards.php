<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 引入数据库配置
require_once '../../db.php';

$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            if (isset($_GET['action'])) {
                switch ($_GET['action']) {
                    case 'list':
                        handleGetCards();
                        break;
                    case 'stats':
                        handleGetCardStats();
                        break;
                    case 'export':
                        handleExportCards();
                        break;
                    default:
                        throw new Exception('不支持的操作');
                }
            } else {
                handleGetCards();
            }
            break;
        case 'POST':
            handleGenerateCards();
            break;
        case 'DELETE':
            handleDeleteCards();
            break;
        default:
            throw new Exception('不支持的请求方法');
    }
} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'message' => $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}

function handleGetCards() {
    global $mysqli;
    
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    $status = isset($_GET['status']) ? $_GET['status'] : '';
    
    $offset = ($page - 1) * $limit;
    
    // 构建查询条件
    $whereConditions = [];
    $params = [];
    $types = '';
    
    if (!empty($search)) {
        $whereConditions[] = "kami LIKE ?";
        $params[] = "%$search%";
        $types .= 's';
    }
    
    if ($status !== '') {
        if ($status === 'unused') {
            $whereConditions[] = "kamicode = '0'";
        } elseif ($status === 'used') {
            $whereConditions[] = "kamicode = '1'";
        }
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    // 获取总数
    $countQuery = "SELECT COUNT(*) as total FROM kamis $whereClause";
    if (!empty($params)) {
        $countStmt = $mysqli->prepare($countQuery);
        if (!empty($types)) {
            $countStmt->bind_param($types, ...$params);
        }
        $countStmt->execute();
        $totalCount = $countStmt->get_result()->fetch_assoc()['total'];
    } else {
        $totalCount = $mysqli->query($countQuery)->fetch_assoc()['total'];
    }
    
    // 获取卡密列表
    $query = "
        SELECT 
            kami,
            kamicode,
            kamitimes,
            time,
            CASE 
                WHEN kamicode = '0' THEN '未使用'
                WHEN kamicode = '1' THEN '已使用'
                ELSE '未知'
            END as status_text
        FROM kamis 
        $whereClause
        ORDER BY time DESC 
        LIMIT ? OFFSET ?
    ";
    
    $stmt = $mysqli->prepare($query);
    $params[] = $limit;
    $params[] = $offset;
    $types .= 'ii';
    
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $cards = [];
    while ($row = $result->fetch_assoc()) {
        $cards[] = $row;
    }
    
    echo json_encode([
        'code' => 200,
        'message' => '获取卡密列表成功',
        'data' => [
            'cards' => $cards,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $limit,
                'total' => (int)$totalCount,
                'total_pages' => ceil($totalCount / $limit)
            ]
        ]
    ], JSON_UNESCAPED_UNICODE);
}

function handleGetCardStats() {
    global $mysqli;
    
    // 获取卡密统计数据
    $statsQuery = "
        SELECT 
            COUNT(*) as total_cards,
            SUM(CASE WHEN kamicode = '0' THEN 1 ELSE 0 END) as unused_cards,
            SUM(CASE WHEN kamicode = '1' THEN 1 ELSE 0 END) as used_cards,
            SUM(CASE WHEN DATE(time) = CURDATE() THEN 1 ELSE 0 END) as today_generated
        FROM kamis
    ";
    
    $result = $mysqli->query($statsQuery);
    $stats = $result->fetch_assoc();
    
    // 获取最近7天的生成趋势
    $trendQuery = "
        SELECT 
            DATE(time) as date,
            COUNT(*) as count
        FROM kamis 
        WHERE time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        GROUP BY DATE(time)
        ORDER BY date ASC
    ";
    
    $trendResult = $mysqli->query($trendQuery);
    $trend = [];
    while ($row = $trendResult->fetch_assoc()) {
        $trend[] = $row;
    }
    
    // 获取不同天数的卡密分布
    $distributionQuery = "
        SELECT 
            kamitimes,
            COUNT(*) as count
        FROM kamis
        GROUP BY kamitimes
        ORDER BY kamitimes ASC
    ";
    
    $distributionResult = $mysqli->query($distributionQuery);
    $distribution = [];
    while ($row = $distributionResult->fetch_assoc()) {
        $distribution[] = $row;
    }
    
    echo json_encode([
        'code' => 200,
        'message' => '获取卡密统计成功',
        'data' => [
            'total_cards' => (int)$stats['total_cards'],
            'unused_cards' => (int)$stats['unused_cards'],
            'used_cards' => (int)$stats['used_cards'],
            'today_generated' => (int)$stats['today_generated'],
            'trend' => $trend,
            'distribution' => $distribution
        ]
    ], JSON_UNESCAPED_UNICODE);
}

function handleGenerateCards() {
    global $mysqli;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['count']) || $input['count'] <= 0) {
        throw new Exception('生成数量必须大于0');
    }
    
    if (!isset($input['days']) || $input['days'] <= 0) {
        throw new Exception('卡密天数必须大于0');
    }
    
    $count = (int)$input['count'];
    $days = (int)$input['days'];
    
    if ($count > 1000) {
        throw new Exception('单次生成数量不能超过1000张');
    }
    
    $generatedCards = [];
    
    // 开始事务
    $mysqli->begin_transaction();
    
    try {
        for ($i = 0; $i < $count; $i++) {
            // 生成唯一卡密
            do {
                $kami = generateCardCode();
                $checkQuery = "SELECT COUNT(*) as count FROM kamis WHERE kami = ?";
                $checkStmt = $mysqli->prepare($checkQuery);
                $checkStmt->bind_param('s', $kami);
                $checkStmt->execute();
                $exists = $checkStmt->get_result()->fetch_assoc()['count'] > 0;
            } while ($exists);
            
            // 插入卡密
            $insertQuery = "INSERT INTO kamis (kami, kamicode, kamitimes, time) VALUES (?, '0', ?, NOW())";
            $insertStmt = $mysqli->prepare($insertQuery);
            $insertStmt->bind_param('si', $kami, $days);
            $insertStmt->execute();
            
            $generatedCards[] = [
                'kami' => $kami,
                'days' => $days
            ];
        }
        
        // 提交事务
        $mysqli->commit();
        
        echo json_encode([
            'code' => 200,
            'message' => "成功生成 {$count} 张卡密",
            'data' => [
                'count' => $count,
                'days' => $days,
                'cards' => $generatedCards
            ]
        ], JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        // 回滚事务
        $mysqli->rollback();
        throw $e;
    }
}

function handleExportCards() {
    global $mysqli;
    
    $status = isset($_GET['status']) ? $_GET['status'] : '';
    $days = isset($_GET['days']) ? (int)$_GET['days'] : 0;
    
    // 构建查询条件
    $whereConditions = [];
    $params = [];
    $types = '';
    
    if ($status === 'unused') {
        $whereConditions[] = "kamicode = '0'";
    } elseif ($status === 'used') {
        $whereConditions[] = "kamicode = '1'";
    }
    
    if ($days > 0) {
        $whereConditions[] = "kamitimes = ?";
        $params[] = $days;
        $types .= 'i';
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    $query = "SELECT kami, kamitimes, kamicode, time FROM kamis $whereClause ORDER BY time DESC";
    
    if (!empty($params)) {
        $stmt = $mysqli->prepare($query);
        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        $result = $stmt->get_result();
    } else {
        $result = $mysqli->query($query);
    }
    
    $cards = [];
    while ($row = $result->fetch_assoc()) {
        $cards[] = $row;
    }
    
    echo json_encode([
        'code' => 200,
        'message' => '导出卡密成功',
        'data' => [
            'cards' => $cards,
            'total' => count($cards)
        ]
    ], JSON_UNESCAPED_UNICODE);
}

function handleDeleteCards() {
    global $mysqli;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['action'])) {
        throw new Exception('操作类型不能为空');
    }
    
    $action = $input['action'];
    
    switch ($action) {
        case 'delete_used':
            // 删除已使用的卡密
            $deleteQuery = "DELETE FROM kamis WHERE kamicode = '1'";
            $result = $mysqli->query($deleteQuery);
            
            echo json_encode([
                'code' => 200,
                'message' => '已删除所有已使用的卡密',
                'data' => ['deleted_count' => $mysqli->affected_rows]
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'delete_single':
            if (!isset($input['kami'])) {
                throw new Exception('卡密不能为空');
            }
            
            $kami = $input['kami'];
            $deleteQuery = "DELETE FROM kamis WHERE kami = ?";
            $stmt = $mysqli->prepare($deleteQuery);
            $stmt->bind_param('s', $kami);
            $stmt->execute();
            
            echo json_encode([
                'code' => 200,
                'message' => '卡密删除成功',
                'data' => null
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        default:
            throw new Exception('不支持的操作');
    }
}

function generateCardCode() {
    $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $code = '';
    
    // 生成格式：XXXX-XXXX-XXXX-XXXX
    for ($i = 0; $i < 4; $i++) {
        if ($i > 0) $code .= '-';
        for ($j = 0; $j < 4; $j++) {
            $code .= $characters[rand(0, strlen($characters) - 1)];
        }
    }
    
    return $code;
}

$mysqli->close();
?>
